/**
 * WCAG-066: Error Prevention Enhanced Check (3.3.6 Level AAA)
 * 85% Automated - Enhanced error prevention for all user input
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface ErrorPreventionEnhancedConfig extends EnhancedCheckConfig {
  enableComprehensiveFormAnalysis?: boolean;
  enableAllInputValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableAdvancedErrorDetection?: boolean;
  enablePreventionMethodValidation?: boolean;
}

export class ErrorPreventionEnhancedCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: ErrorPreventionEnhancedConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ErrorPreventionEnhancedConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableComprehensiveFormAnalysis: true,
      enableAllInputValidation: true,
      enableAccessibilityPatterns: true,
      enableAdvancedErrorDetection: true,
      enablePreventionMethodValidation: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-066',
      'Error Prevention Enhanced',
      'robust',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeErrorPreventionEnhancedCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with comprehensive error prevention analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-066',
        ruleName: 'Error Prevention Enhanced',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'enhanced-error-prevention-analysis',
          comprehensiveFormAnalysis: enhancedConfig.enableComprehensiveFormAnalysis,
          allInputValidation: enhancedConfig.enableAllInputValidation,
          advancedErrorDetection: enhancedConfig.enableAdvancedErrorDetection,
          preventionMethodValidation: enhancedConfig.enablePreventionMethodValidation,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 40,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeErrorPreventionEnhancedCheck(
    page: Page,
    _config: ErrorPreventionEnhancedConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze error prevention mechanisms
    const errorPreventionAnalysis = await page.evaluate(() => {
      const formsWithIssues: Array<{
        selector: string;
        element: string;
        hasValidation: boolean;
        hasConfirmation: boolean;
        hasUndo: boolean;
        hasReview: boolean;
        isReversible: boolean;
        riskLevel: 'low' | 'medium' | 'high' | 'critical';
        fieldCount: number;
        criticalFields: string[];
        preventionMechanisms: string[];
        missingMechanisms: string[];
      }> = [];

      const inputsWithIssues: Array<{
        selector: string;
        element: string;
        type: string;
        hasValidation: boolean;
        hasConfirmation: boolean;
        isRequired: boolean;
        isCritical: boolean;
        riskLevel: 'low' | 'medium' | 'high' | 'critical';
        preventionMechanisms: string[];
        missingMechanisms: string[];
      }> = [];

      // Analyze forms
      const forms = document.querySelectorAll('form');
      forms.forEach((form, index) => {
        const formSelector = generateSelector(form, index);
        const inputs = form.querySelectorAll('input, textarea, select');

        // Determine risk level based on form content
        const riskLevel = determineFormRiskLevel(form);

        // Check for error prevention mechanisms
        const hasValidation = checkFormValidation(form);
        const hasConfirmation = checkConfirmationMechanism(form);
        const hasUndo = checkUndoMechanism(form);
        const hasReview = checkReviewMechanism(form);
        const isReversible = checkReversibility(form);

        const criticalFields = getCriticalFields(form);
        const preventionMechanisms = getPreventionMechanisms(form);
        const missingMechanisms = getMissingMechanisms(form, riskLevel);

        if (missingMechanisms.length > 0) {
          formsWithIssues.push({
            selector: formSelector,
            element: 'form',
            hasValidation,
            hasConfirmation,
            hasUndo,
            hasReview,
            isReversible,
            riskLevel,
            fieldCount: inputs.length,
            criticalFields,
            preventionMechanisms,
            missingMechanisms,
          });
        }
      });

      // Analyze individual inputs
      const allInputs = document.querySelectorAll('input, textarea, select');
      allInputs.forEach((input, index) => {
        const inputSelector = generateSelector(input, index);
        const inputType = input.getAttribute('type') || input.tagName.toLowerCase();
        const isRequired = input.hasAttribute('required');
        const isCritical = isCriticalInput(input);
        const riskLevel = determineInputRiskLevel(input);

        const hasValidation = checkInputValidation(input);
        const hasConfirmation = checkInputConfirmation(input);

        const preventionMechanisms = getInputPreventionMechanisms(input);
        const missingMechanisms = getInputMissingMechanisms(input, riskLevel);

        if (missingMechanisms.length > 0 && (isCritical || riskLevel !== 'low')) {
          inputsWithIssues.push({
            selector: inputSelector,
            element: input.tagName.toLowerCase(),
            type: inputType,
            hasValidation,
            hasConfirmation,
            isRequired,
            isCritical,
            riskLevel,
            preventionMechanisms,
            missingMechanisms,
          });
        }
      });

      return {
        formsWithIssues,
        inputsWithIssues,
        totalForms: forms.length,
        totalInputs: allInputs.length,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter((c) => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function determineFormRiskLevel(form: Element): 'low' | 'medium' | 'high' | 'critical' {
        const criticalKeywords = ['delete', 'remove', 'cancel', 'terminate', 'close', 'deactivate'];
        const highRiskKeywords = ['payment', 'purchase', 'order', 'submit', 'send', 'transfer'];
        const mediumRiskKeywords = ['update', 'change', 'modify', 'edit', 'save'];

        const formText = form.textContent?.toLowerCase() || '';
        const action = form.getAttribute('action')?.toLowerCase() || '';
        const method = form.getAttribute('method')?.toLowerCase() || '';

        if (
          criticalKeywords.some((keyword) => formText.includes(keyword) || action.includes(keyword))
        ) {
          return 'critical';
        }
        if (
          highRiskKeywords.some((keyword) => formText.includes(keyword) || action.includes(keyword))
        ) {
          return 'high';
        }
        if (
          mediumRiskKeywords.some(
            (keyword) => formText.includes(keyword) || action.includes(keyword),
          )
        ) {
          return 'medium';
        }
        if (method === 'post' || form.querySelectorAll('input').length > 5) {
          return 'medium';
        }
        return 'low';
      }

      function determineInputRiskLevel(input: Element): 'low' | 'medium' | 'high' | 'critical' {
        const type = input.getAttribute('type')?.toLowerCase() || '';
        const name = input.getAttribute('name')?.toLowerCase() || '';
        const id = input.getAttribute('id')?.toLowerCase() || '';

        const criticalTypes = ['password'];
        const highRiskTypes = ['email', 'tel', 'url'];
        const criticalNames = ['password', 'ssn', 'social', 'credit', 'card', 'account'];
        const highRiskNames = ['email', 'phone', 'address', 'payment'];

        if (
          criticalTypes.includes(type) ||
          criticalNames.some((name) => name.includes(name) || id.includes(name))
        ) {
          return 'critical';
        }
        if (
          highRiskTypes.includes(type) ||
          highRiskNames.some((name) => name.includes(name) || id.includes(name))
        ) {
          return 'high';
        }
        if (input.hasAttribute('required') || type === 'number' || type === 'date') {
          return 'medium';
        }
        return 'low';
      }

      function checkFormValidation(form: Element): boolean {
        return (
          form.hasAttribute('novalidate') === false &&
          form.querySelectorAll('[required], [pattern], [min], [max], [minlength], [maxlength]')
            .length > 0
        );
      }

      function checkConfirmationMechanism(form: Element): boolean {
        const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
        return (
          Array.from(submitButtons).some((button) => {
            const onclick = button.getAttribute('onclick') || '';
            return onclick.includes('confirm') || onclick.includes('alert');
          }) || form.querySelector('.confirmation, .confirm-dialog') !== null
        );
      }

      function checkUndoMechanism(form: Element): boolean {
        return form.querySelector('button[type="reset"], .undo, .cancel, .revert') !== null;
      }

      function checkReviewMechanism(form: Element): boolean {
        return form.querySelector('.review, .preview, .summary, .confirm-details') !== null;
      }

      function checkReversibility(form: Element): boolean {
        const action = form.getAttribute('action')?.toLowerCase() || '';
        const formText = form.textContent?.toLowerCase() || '';
        return (
          !action.includes('delete') &&
          !formText.includes('permanent') &&
          !formText.includes('irreversible')
        );
      }

      function getCriticalFields(form: Element): string[] {
        const criticalFields: string[] = [];
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach((input) => {
          if (isCriticalInput(input)) {
            const label =
              form.querySelector(`label[for="${input.id}"]`)?.textContent?.trim() ||
              input.getAttribute('placeholder') ||
              input.getAttribute('name') ||
              'unnamed field';
            criticalFields.push(label);
          }
        });

        return criticalFields;
      }

      function isCriticalInput(input: Element): boolean {
        const type = input.getAttribute('type')?.toLowerCase() || '';
        const name = input.getAttribute('name')?.toLowerCase() || '';
        const id = input.getAttribute('id')?.toLowerCase() || '';

        const criticalPatterns = [
          'password',
          'ssn',
          'social',
          'credit',
          'card',
          'account',
          'delete',
          'remove',
        ];
        return criticalPatterns.some(
          (pattern) => type.includes(pattern) || name.includes(pattern) || id.includes(pattern),
        );
      }

      function getPreventionMechanisms(form: Element): string[] {
        const mechanisms: string[] = [];

        if (checkFormValidation(form)) mechanisms.push('validation');
        if (checkConfirmationMechanism(form)) mechanisms.push('confirmation');
        if (checkUndoMechanism(form)) mechanisms.push('undo');
        if (checkReviewMechanism(form)) mechanisms.push('review');
        if (checkReversibility(form)) mechanisms.push('reversible');

        return mechanisms;
      }

      function getMissingMechanisms(form: Element, riskLevel: string): string[] {
        const missing: string[] = [];

        // All forms should have validation
        if (!checkFormValidation(form)) missing.push('validation');

        // Medium+ risk forms should have confirmation
        if (riskLevel !== 'low' && !checkConfirmationMechanism(form)) {
          missing.push('confirmation');
        }

        // High+ risk forms should have review or undo
        if (
          (riskLevel === 'high' || riskLevel === 'critical') &&
          !checkReviewMechanism(form) &&
          !checkUndoMechanism(form)
        ) {
          missing.push('review-or-undo');
        }

        // Critical forms should have multiple mechanisms
        if (riskLevel === 'critical') {
          if (!checkReviewMechanism(form)) missing.push('review');
          if (!checkUndoMechanism(form)) missing.push('undo');
        }

        return missing;
      }

      function checkInputValidation(input: Element): boolean {
        return (
          input.hasAttribute('required') ||
          input.hasAttribute('pattern') ||
          input.hasAttribute('min') ||
          input.hasAttribute('max') ||
          input.hasAttribute('minlength') ||
          input.hasAttribute('maxlength') ||
          input.getAttribute('type') === 'email' ||
          input.getAttribute('type') === 'url' ||
          input.getAttribute('type') === 'tel'
        );
      }

      function checkInputConfirmation(input: Element): boolean {
        const name = input.getAttribute('name') || '';
        const id = input.getAttribute('id') || '';

        // Look for confirmation fields
        if (name.includes('password') || id.includes('password')) {
          const form = input.closest('form');
          return (
            form?.querySelector(
              '[name*="confirm"], [id*="confirm"], [name*="repeat"], [id*="repeat"]',
            ) !== null
          );
        }

        return false;
      }

      function getInputPreventionMechanisms(input: Element): string[] {
        const mechanisms: string[] = [];

        if (checkInputValidation(input)) mechanisms.push('validation');
        if (checkInputConfirmation(input)) mechanisms.push('confirmation');

        return mechanisms;
      }

      function getInputMissingMechanisms(input: Element, riskLevel: string): string[] {
        const missing: string[] = [];

        if (riskLevel !== 'low' && !checkInputValidation(input)) {
          missing.push('validation');
        }

        if (riskLevel === 'critical' && !checkInputConfirmation(input)) {
          missing.push('confirmation');
        }

        return missing;
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalIssues =
      errorPreventionAnalysis.formsWithIssues.length +
      errorPreventionAnalysis.inputsWithIssues.length;

    if (totalIssues > 0) {
      // Calculate score based on severity and number of issues
      const criticalIssues = [
        ...errorPreventionAnalysis.formsWithIssues.filter((f) => f.riskLevel === 'critical'),
        ...errorPreventionAnalysis.inputsWithIssues.filter((i) => i.riskLevel === 'critical'),
      ];

      const highRiskIssues = [
        ...errorPreventionAnalysis.formsWithIssues.filter((f) => f.riskLevel === 'high'),
        ...errorPreventionAnalysis.inputsWithIssues.filter((i) => i.riskLevel === 'high'),
      ];

      if (criticalIssues.length > 0) {
        score -= Math.min(60, criticalIssues.length * 20);
        issues.push(
          `${criticalIssues.length} critical forms/inputs lack adequate error prevention`,
        );
      }

      if (highRiskIssues.length > 0) {
        score -= Math.min(30, highRiskIssues.length * 10);
        issues.push(`${highRiskIssues.length} high-risk forms/inputs need better error prevention`);
      }

      const remainingIssues = totalIssues - criticalIssues.length - highRiskIssues.length;
      if (remainingIssues > 0) {
        score -= Math.min(20, remainingIssues * 5);
        issues.push(`${remainingIssues} forms/inputs could benefit from enhanced error prevention`);
      }

      evidence.push({
        type: 'interaction',
        description: 'Error prevention analysis',
        value: `Found ${totalIssues} forms/inputs with inadequate error prevention mechanisms`,
        elementCount: totalIssues,
        affectedSelectors: [
          ...errorPreventionAnalysis.formsWithIssues.map((f) => f.selector),
          ...errorPreventionAnalysis.inputsWithIssues.map((i) => i.selector),
        ],
        severity:
          criticalIssues.length > 0 ? 'error' : highRiskIssues.length > 0 ? 'warning' : 'info',
        fixExample: {
          before:
            '<form><input type="password" name="password"><button type="submit">Delete Account</button></form>',
          after:
            '<form onsubmit="return confirmDeletion()"><input type="password" name="password" required minlength="8"><input type="password" name="confirm_password" required><button type="submit">Delete Account</button></form>',
          description: 'Implement comprehensive error prevention for critical operations',
          codeExample: `
<!-- Before: Inadequate error prevention -->
<form action="/delete-account" method="post">
  <input type="password" name="password">
  <button type="submit">Delete Account</button>
</form>

<!-- After: Enhanced error prevention -->
<form action="/delete-account" method="post" onsubmit="return confirmDeletion()">
  <div class="field">
    <label for="password">Current Password</label>
    <input type="password" id="password" name="password" required minlength="8"
           aria-describedby="pwd-help">
    <div id="pwd-help">Enter your current password to confirm</div>
  </div>
  
  <div class="field">
    <label for="confirm-password">Confirm Password</label>
    <input type="password" id="confirm-password" name="confirm_password" required
           aria-describedby="confirm-help">
    <div id="confirm-help">Re-enter your password</div>
  </div>
  
  <div class="confirmation">
    <label>
      <input type="checkbox" required> 
      I understand this action cannot be undone
    </label>
  </div>
  
  <div class="actions">
    <button type="button" onclick="history.back()">Cancel</button>
    <button type="submit" class="danger">Delete Account</button>
  </div>
</form>

<script>
function confirmDeletion() {
  return confirm('Are you sure you want to permanently delete your account? This action cannot be undone.');
}
</script>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/error-prevention-all.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/general/G164',
            'https://www.w3.org/WAI/WCAG21/Techniques/general/G98',
          ],
        },
        metadata: {
          scanDuration,
          elementsAnalyzed:
            errorPreventionAnalysis.totalForms + errorPreventionAnalysis.totalInputs,
          checkSpecificData: {
            totalForms: errorPreventionAnalysis.totalForms,
            totalInputs: errorPreventionAnalysis.totalInputs,
            formsWithIssues: errorPreventionAnalysis.formsWithIssues.length,
            inputsWithIssues: errorPreventionAnalysis.inputsWithIssues.length,
            criticalIssues: criticalIssues.length,
            highRiskIssues: highRiskIssues.length,
            criticalCount: criticalIssues.length,
            highRiskCount: highRiskIssues.length,
            mediumRiskCount: totalIssues - criticalIssues.length - highRiskIssues.length,
          },
        },
      });

      // Add specific examples for problematic forms
      errorPreventionAnalysis.formsWithIssues.slice(0, 5).forEach((form) => {
        evidence.push({
          type: 'interaction',
          description: `Form lacks error prevention (${form.riskLevel} risk)`,
          value: `${form.fieldCount} fields, missing: ${form.missingMechanisms.join(', ')}`,
          selector: form.selector,
          severity:
            form.riskLevel === 'critical'
              ? 'error'
              : form.riskLevel === 'high'
                ? 'warning'
                : 'info',
          metadata: {
            checkSpecificData: {
              riskLevel: form.riskLevel,
              fieldCount: form.fieldCount,
              criticalFields: form.criticalFields.join(', '),
              preventionMechanisms: form.preventionMechanisms.join(', '),
              missingMechanisms: form.missingMechanisms.join(', '),
            },
          },
        });
      });

      recommendations.push('Implement validation for all user inputs');
      recommendations.push('Add confirmation dialogs for high-risk operations');
      recommendations.push('Provide review/preview functionality for critical forms');
      recommendations.push('Include undo mechanisms where possible');

      if (criticalIssues.length > 0) {
        recommendations.push(
          'CRITICAL: Add multiple error prevention mechanisms for critical operations',
        );
        recommendations.push('Require confirmation fields for password changes and deletions');
      }
    } else {
      // No error prevention issues found
      evidence.push({
        type: 'info',
        description: 'Adequate error prevention mechanisms detected',
        value: `All ${errorPreventionAnalysis.totalForms} forms have appropriate error prevention`,
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed:
            errorPreventionAnalysis.totalForms + errorPreventionAnalysis.totalInputs,
          checkSpecificData: {
            totalForms: errorPreventionAnalysis.totalForms,
            totalInputs: errorPreventionAnalysis.totalInputs,
            allFormsHavePreventionMechanisms: true,
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
