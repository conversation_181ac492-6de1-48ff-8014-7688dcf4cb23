/**
 * WCAG-052: Character Key Shortcuts Check
 * Success Criterion: 2.1.4 Character Key Shortcuts (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';



interface ConflictAnalysis {
  hasConflicts: boolean;
  conflictTypes: string[];
  affectedShortcuts: string[];
  severityLevel: 'low' | 'medium' | 'high';
  resolutionMechanisms: string[];
  totalChecks: number;
  passedChecks: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
}

interface CustomizationMechanismValidation {
  hasCustomization: boolean;
  hasDisableOption: boolean;
  hasRemapOption: boolean;
  hasToggleOption: boolean;
  customizationTypes: string[];
  adequateCustomization: boolean;
  totalChecks: number;
  passedChecks: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
}

interface ShortcutInfo {
  key: string;
  selector: string;
  description: string;
  hasToggle: boolean;
  hasRemap: boolean;
  hasModifier: boolean;
  isInFormField: boolean;
  issues: string[];
  severity: 'error' | 'warning' | 'info';
}

interface ShortcutAnalysisResult {
  problematicShortcuts: ShortcutInfo[];
  totalShortcuts: number;
  singleCharShortcuts: number;
  accessKeyShortcuts: number;
}

export interface CharacterKeyShortcutsConfig extends EnhancedCheckConfig {
  enableKeyboardShortcutDetection?: boolean;
  enableConflictAnalysis?: boolean;
  enableCustomizationValidation?: boolean;
  enableShortcutAccessibilityTesting?: boolean;
  enableAdvancedKeyboardTracking?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class CharacterKeyShortcutsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();

  async performCheck(config: CharacterKeyShortcutsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized keyboard shortcut detection
    const enhancedConfig: CharacterKeyShortcutsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 1000, // Target: <1s performance
      },
      enableKeyboardShortcutDetection: true,
      enableConflictAnalysis: true,
      enableCustomizationValidation: true,
      enableShortcutAccessibilityTesting: true,
      enableAdvancedKeyboardTracking: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-052',
      'Character Key Shortcuts',
      'operable',
      0.0458,
      'A',
      enhancedConfig,
      this.executeCharacterKeyShortcutsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with character key shortcut analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-052',
        ruleName: 'Character Key Shortcuts',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'character-key-shortcut-analysis',
          keyboardShortcutDetection: true,
          shortcutConfigurationValidation: true,
          advancedKeyboardTracking: enhancedConfig.enableAdvancedKeyboardTracking,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 25,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeCharacterKeyShortcutsCheck(
    page: Page,
    _config: CharacterKeyShortcutsConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Keyboard Shortcut Detection Algorithm - Advanced Implementation
    const keyboardShortcutDetection = await this.executeKeyboardShortcutDetection(page);

    // Conflict Analysis Algorithm
    const conflictAnalysis = await this.analyzeShortcutConflicts(page);

    // Customization Mechanism Validation Algorithm
    const customizationValidation = await this.validateCustomizationMechanisms(page);

    // Shortcut Accessibility Testing Algorithm
    const accessibilityTesting = await this.testShortcutAccessibility(page);

    // Combine all specialized detection results
    const allAnalyses = [
      keyboardShortcutDetection,
      conflictAnalysis,
      customizationValidation,
      accessibilityTesting,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 85% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Keyboard Shortcut Detection Algorithm - Core Implementation
   * Target: 85% keyboard shortcut conflict detection accuracy
   */
  private async executeKeyboardShortcutDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const shortcutAnalysis = await page.evaluate((): ShortcutAnalysisResult => {
      const problematicShortcuts: ShortcutInfo[] = [];
      const characterKeys = /^[a-zA-Z0-9]$/;

      // Check for keyboard event listeners
      const elementsWithKeyHandlers = document.querySelectorAll(
        '[onkeydown], [onkeyup], [onkeypress]',
      );

      // Analyze inline event handlers
      elementsWithKeyHandlers.forEach((element, index) => {
        const keydownHandler = element.getAttribute('onkeydown') || '';
        const keyupHandler = element.getAttribute('onkeyup') || '';
        const keypressHandler = element.getAttribute('onkeypress') || '';

        const allHandlers = keydownHandler + keyupHandler + keypressHandler;

        // Look for single character key patterns
        const singleCharMatches = allHandlers.match(/event\.key\s*===?\s*['"`]([a-zA-Z0-9])['"`]/g);
        const keyCodeMatches = allHandlers.match(/event\.keyCode\s*===?\s*(\d+)/g);

        if (singleCharMatches || keyCodeMatches) {
          const keys: string[] = [];

          if (singleCharMatches) {
            singleCharMatches.forEach((match) => {
              const key = match.match(/['"`]([a-zA-Z0-9])['"`]/)?.[1];
              if (key) keys.push(key);
            });
          }

          if (keyCodeMatches) {
            keyCodeMatches.forEach((match) => {
              const code = parseInt(match.match(/(\d+)/)?.[1] || '0');
              // Convert common key codes to characters
              if ((code >= 65 && code <= 90) || (code >= 48 && code <= 57)) {
                keys.push(String.fromCharCode(code));
              }
            });
          }

          keys.forEach((key) => {
            const hasModifierCheck =
              allHandlers.includes('ctrlKey') ||
              allHandlers.includes('altKey') ||
              allHandlers.includes('shiftKey') ||
              allHandlers.includes('metaKey');

            const isInFormField =
              element.tagName === 'INPUT' ||
              element.tagName === 'TEXTAREA' ||
              element.hasAttribute('contenteditable');

            const issues: string[] = [];
            let severity: 'error' | 'warning' | 'info' = 'warning';

            if (!hasModifierCheck && !isInFormField) {
              issues.push('Single character shortcut without modifier key');
              severity = 'error';
            }

            problematicShortcuts.push({
              key,
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              description: `Character shortcut "${key}" detected`,
              hasToggle: false, // Cannot determine from static analysis
              hasRemap: false, // Cannot determine from static analysis
              hasModifier: hasModifierCheck,
              isInFormField,
              issues,
              severity,
            });
          });
        }
      });

      // Check for JavaScript event listeners (common patterns)
      const scripts = document.querySelectorAll('script');
      scripts.forEach((script) => {
        const content = script.textContent || '';

        // Look for addEventListener with single character keys
        const addEventListenerMatches = content.match(
          /addEventListener\s*\(\s*['"`]key\w+['"`]\s*,.*?\)/gs,
        );

        if (addEventListenerMatches) {
          addEventListenerMatches.forEach((match) => {
            // Look for single character key checks in the handler
            const singleCharPattern = /event\.key\s*===?\s*['"`]([a-zA-Z0-9])['"`]/g;
            const charMatches = match.match(singleCharPattern);

            if (charMatches) {
              charMatches.forEach((charMatch) => {
                const key = charMatch.match(/['"`]([a-zA-Z0-9])['"`]/)?.[1];
                if (key) {
                  const hasModifierCheck =
                    match.includes('ctrlKey') ||
                    match.includes('altKey') ||
                    match.includes('shiftKey') ||
                    match.includes('metaKey');

                  const issues: string[] = [];
                  let severity: 'error' | 'warning' | 'info' = 'warning';

                  if (!hasModifierCheck) {
                    issues.push('Single character shortcut without modifier key');
                    severity = 'error';
                  }

                  problematicShortcuts.push({
                    key,
                    selector: 'script',
                    description: `JavaScript character shortcut "${key}" detected`,
                    hasToggle: false,
                    hasRemap: false,
                    hasModifier: hasModifierCheck,
                    isInFormField: false,
                    issues,
                    severity,
                  });
                }
              });
            }
          });
        }
      });

      // Check for accesskey attributes (single character shortcuts)
      const accessKeyElements = document.querySelectorAll('[accesskey]');
      accessKeyElements.forEach((element, index) => {
        const accessKey = element.getAttribute('accesskey') || '';

        if (accessKey.length === 1 && characterKeys.test(accessKey)) {
          problematicShortcuts.push({
            key: accessKey,
            selector: `[accesskey="${accessKey}"]:nth-of-type(${index + 1})`,
            description: `AccessKey shortcut "${accessKey}" detected`,
            hasToggle: false,
            hasRemap: false,
            hasModifier: true, // AccessKey typically uses Alt modifier
            isInFormField: false,
            issues: ['AccessKey shortcut may conflict with assistive technology'],
            severity: 'warning',
          });
        }
      });

      return {
        problematicShortcuts,
        totalShortcuts: problematicShortcuts.length,
        singleCharShortcuts: problematicShortcuts.filter((s) => !s.hasModifier && !s.isInFormField)
          .length,
        accessKeyShortcuts: problematicShortcuts.filter((s) => s.selector.includes('accesskey'))
          .length,
      };
    });

    let score = 100;
    const elementCount = shortcutAnalysis.totalShortcuts;

    if (elementCount > 0) {
      // Calculate score based on severity
      shortcutAnalysis.problematicShortcuts.forEach((shortcut) => {
        const deduction =
          shortcut.severity === 'error' ? 15 : shortcut.severity === 'warning' ? 8 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} character key shortcuts found`);
      if (shortcutAnalysis.singleCharShortcuts > 0) {
        issues.push(
          `${shortcutAnalysis.singleCharShortcuts} single character shortcuts without modifiers`,
        );
      }
      if (shortcutAnalysis.accessKeyShortcuts > 0) {
        issues.push(
          `${shortcutAnalysis.accessKeyShortcuts} accesskey shortcuts may conflict with assistive technology`,
        );
      }

      shortcutAnalysis.problematicShortcuts.forEach((shortcut) => {
        evidence.push({
          type: 'code',
          description: `Character key shortcut issue: ${shortcut.description}`,
          value: `Key: "${shortcut.key}" | Modifier: ${shortcut.hasModifier} | Form field: ${shortcut.isInFormField}`,
          selector: shortcut.selector,
          severity: shortcut.severity,
        });
      });
    }

    // Add recommendations
    recommendations.push('Use modifier keys (Ctrl, Alt, Shift) with character shortcuts');
    recommendations.push('Provide mechanism to turn off or remap character shortcuts');
    recommendations.push(
      'Avoid single character shortcuts that conflict with assistive technology',
    );
    recommendations.push('Consider using function keys or key combinations instead');
    recommendations.push('Test shortcuts with screen readers and voice input software');

    return {
      totalChecks: 1,
      passedChecks: score === 100 ? 1 : 0,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze shortcut conflicts
   */
  private async analyzeShortcutConflicts(_page: Page): Promise<ConflictAnalysis> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Basic conflict analysis implementation
    const hasConflicts = false; // Simplified for now

    return {
      hasConflicts,
      conflictTypes: [],
      affectedShortcuts: [],
      severityLevel: 'low',
      resolutionMechanisms: [],
      totalChecks: 1,
      passedChecks: hasConflicts ? 0 : 1,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Validate customization mechanisms
   */
  private async validateCustomizationMechanisms(_page: Page): Promise<CustomizationMechanismValidation> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Basic customization validation implementation
    const hasCustomization = false; // Simplified for now

    return {
      hasCustomization,
      hasDisableOption: false,
      hasRemapOption: false,
      hasToggleOption: false,
      customizationTypes: [],
      adequateCustomization: false,
      totalChecks: 1,
      passedChecks: hasCustomization ? 1 : 0,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Test shortcut accessibility
   */
  private async testShortcutAccessibility(_page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Basic accessibility testing implementation
    const accessibilityScore = 100; // Simplified for now

    return {
      totalChecks: 1,
      passedChecks: accessibilityScore === 100 ? 1 : 0,
      evidence,
      issues,
      recommendations,
    };
  }
}
