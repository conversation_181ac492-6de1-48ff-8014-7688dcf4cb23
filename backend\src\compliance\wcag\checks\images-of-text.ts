/**
 * WCAG-039: Images of Text Check
 * Success Criterion: 1.4.5 Images of Text (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export interface ImagesOfTextConfig extends EnhancedCheckConfig {
  enableAIImageAnalysis?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableEnhancedColorAnalysis?: boolean;
  enableWideGamutAnalysis?: boolean;
  enableTextContrastDetection?: boolean;
}

export class ImagesOfTextCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();

  async performCheck(config: ImagesOfTextConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ImagesOfTextConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAIImageAnalysis: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
      enableEnhancedColorAnalysis: true,
      enableWideGamutAnalysis: true,
      enableTextContrastDetection: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-039',
      'Images of Text',
      'perceivable',
      0.0535,
      'AA',
      enhancedConfig,
      this.executeImagesOfTextCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with image text analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-039',
        ruleName: 'Images of Text',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'image-text-analysis',
          imageAnalysis: true,
          textDetection: true,
          aiImageAnalysis: enhancedConfig.enableAIImageAnalysis,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 30,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeImagesOfTextCheck(
    page: Page,
    config: ImagesOfTextConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Basic image text analysis (fallback implementation)

    // Analyze images for text content
    const imageAnalysis = await page.evaluate(() => {
      const suspiciousImages: Array<{
        src: string;
        alt: string;
        selector: string;
        width: number;
        height: number;
        isDecorative: boolean;
        hasTextualAlternative: boolean;
        likelyContainsText: boolean;
        textIndicators: string[];
        severity: 'error' | 'warning' | 'info';
        isEssential: boolean;
        colorAnalysis?: {
          hasHighContrast: boolean;
          contrastRatio: number;
          dominantColors: string[];
          textLikePatterns: boolean;
        };
      }> = [];

      // Text-indicating patterns in filenames and alt text
      const textPatterns = [
        /\b(text|title|heading|label|button|menu|nav|logo|brand)\b/i,
        /\b(quote|testimonial|caption|subtitle|header|footer)\b/i,
        /\b(sign|banner|poster|flyer|card|badge)\b/i,
        /\.(png|jpg|jpeg|gif|svg|webp)$/i,
      ];

      // Essential use patterns (logos, branding, etc.)
      const essentialPatterns = [
        /\b(logo|brand|trademark|signature|watermark)\b/i,
        /\b(essential|required|necessary|critical)\b/i,
      ];

      // Get all images
      const images = document.querySelectorAll('img, svg, [role="img"]');

      images.forEach((element, index) => {
        const img = element as HTMLImageElement;
        const src = img.src || img.getAttribute('data-src') || '';
        const alt = img.alt || img.getAttribute('aria-label') || '';
        const rect = img.getBoundingClientRect();

        // Skip very small images (likely icons)
        if (rect.width < 20 || rect.height < 20) return;

        const textIndicators: string[] = [];
        let likelyContainsText = false;
        let isEssential = false;

        // Check filename for text indicators
        const filename = src.split('/').pop() || '';
        if (textPatterns.some((pattern) => pattern.test(filename))) {
          textIndicators.push('Filename suggests text content');
          likelyContainsText = true;
        }

        // Check alt text for text indicators
        if (textPatterns.some((pattern) => pattern.test(alt))) {
          textIndicators.push('Alt text suggests text content');
          likelyContainsText = true;
        }

        // Check for essential use cases
        if (essentialPatterns.some((pattern) => pattern.test(alt + ' ' + filename))) {
          isEssential = true;
          textIndicators.push('Appears to be essential (logo/branding)');
        }

        // Check surrounding context
        const parent = img.parentElement;
        const parentText = parent?.textContent?.toLowerCase() || '';
        if (
          parent &&
          (parentText.includes('logo') ||
            parentText.includes('brand') ||
            parentText.includes('title') ||
            parentText.includes('heading'))
        ) {
          textIndicators.push('Context suggests text content');
          likelyContainsText = true;
        }

        // Check CSS classes for text-related names
        const className = img.className || '';
        if (/\b(text|title|heading|logo|brand|button|label)\b/i.test(className)) {
          textIndicators.push('CSS class suggests text content');
          likelyContainsText = true;
        }

        // Check if image is decorative
        const isDecorative =
          alt === '' ||
          img.getAttribute('role') === 'presentation' ||
          img.getAttribute('aria-hidden') === 'true';

        // Check if there's textual alternative nearby
        const hasTextualAlternative = parent
          ? Array.from(parent.children).some(
              (child) => child !== img && child.textContent?.trim().length > 0,
            )
          : false;

        // Basic color analysis for text detection (simplified)
        const colorAnalysis = undefined;
        // Note: Advanced color analysis would require additional utilities

        // Determine severity
        let severity: 'error' | 'warning' | 'info' = 'info';
        if (likelyContainsText && !isEssential && !hasTextualAlternative) {
          severity = 'error';
        } else if (likelyContainsText && !isEssential) {
          severity = 'warning';
        }

        if (likelyContainsText || textIndicators.length > 0) {
          suspiciousImages.push({
            src: src.substring(0, 100),
            alt: alt.substring(0, 100),
            selector: `img:nth-of-type(${index + 1})`,
            width: rect.width,
            height: rect.height,
            isDecorative,
            hasTextualAlternative,
            likelyContainsText,
            textIndicators,
            severity,
            isEssential,
            colorAnalysis,
          });
        }
      });

      // Check for CSS background images that might contain text
      const elementsWithBg = document.querySelectorAll('*');
      const bgImages: Array<{
        selector: string;
        backgroundImage: string;
        textContent: string;
        hasTextContent: boolean;
      }> = [];

      elementsWithBg.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const bgImage = computedStyle.backgroundImage;

        if (bgImage && bgImage !== 'none' && bgImage.includes('url(')) {
          const textContent = element.textContent?.trim() || '';
          const hasTextContent = textContent.length > 0;

          // If element has background image but no text content, it might be text as image
          if (!hasTextContent || textContent.length < 5) {
            bgImages.push({
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              backgroundImage: bgImage.substring(0, 100),
              textContent,
              hasTextContent,
            });
          }
        }
      });

      return {
        suspiciousImages,
        bgImages,
        totalImages: images.length,
        suspiciousCount: suspiciousImages.length,
        errorCount: suspiciousImages.filter((img) => img.severity === 'error').length,
        warningCount: suspiciousImages.filter((img) => img.severity === 'warning').length,
        essentialCount: suspiciousImages.filter((img) => img.isEssential).length,
        bgImageCount: bgImages.length,
      };
    });

    let score = 100;
    const elementCount = imageAnalysis.suspiciousCount;
    const scanDuration = Date.now() - startTime;

    // Evaluate images of text issues
    if (elementCount > 0) {
      // Calculate score based on severity
      imageAnalysis.suspiciousImages.forEach((image) => {
        const deduction = image.severity === 'error' ? 15 : image.severity === 'warning' ? 8 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} images potentially containing text found`);
      if (imageAnalysis.errorCount > 0) {
        issues.push(`${imageAnalysis.errorCount} images likely contain non-essential text`);
      }
      if (imageAnalysis.warningCount > 0) {
        issues.push(`${imageAnalysis.warningCount} images may contain text without alternatives`);
      }

      imageAnalysis.suspiciousImages.forEach((image) => {
        evidence.push({
          type: 'code',
          description: `Image potentially containing text: ${image.textIndicators.join(', ')}`,
          value: `Alt: "${image.alt}" | Src: ${image.src}`,
          selector: image.selector,
          elementCount: 1,
          affectedSelectors: [image.selector],
          severity: image.severity,
          fixExample: {
            before: this.getBeforeExample(image),
            after: this.getAfterExample(image),
            description: this.getFixDescription(image),
            codeExample: this.getCodeExample(image.isEssential ? 'essential' : 'non_essential'),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/images-of-text.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/C22',
              'https://www.w3.org/WAI/WCAG21/Techniques/C30',
            ],
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              src: image.src,
              alt: image.alt,
              width: image.width,
              height: image.height,
              isEssential: image.isEssential,
              likelyContainsText: image.likelyContainsText,
              textIndicators: image.textIndicators.join(', '),
              hasTextualAlternative: image.hasTextualAlternative,
              colorAnalysis: image.colorAnalysis ? JSON.stringify(image.colorAnalysis) : 'none',
              enhancedColorAnalysisEnabled: config.enableEnhancedColorAnalysis || false,
              wideGamutAnalysisEnabled: config.enableWideGamutAnalysis || false,
            },
          },
        });
      });
    }

    // Check background images
    if (imageAnalysis.bgImageCount > 0) {
      score = Math.max(0, score - imageAnalysis.bgImageCount * 5);
      issues.push(`${imageAnalysis.bgImageCount} background images may contain text`);

      imageAnalysis.bgImages.forEach((bgImg) => {
        evidence.push({
          type: 'code',
          description: 'Background image may contain text',
          value: `Background: ${bgImg.backgroundImage}`,
          selector: bgImg.selector,
          elementCount: 1,
          affectedSelectors: [bgImg.selector],
          severity: 'warning',
          fixExample: {
            before: `<div style="background-image: ${bgImg.backgroundImage}"></div>`,
            after: '<div><h2>Text Content</h2><p>Actual text instead of image</p></div>',
            description: 'Replace background image text with actual HTML text',
            codeExample: this.getCodeExample('background_image'),
            resources: ['https://www.w3.org/WAI/WCAG21/Understanding/images-of-text.html'],
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              backgroundImage: bgImg.backgroundImage,
              hasTextContent: bgImg.hasTextContent,
              textContent: bgImg.textContent,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Use actual HTML text instead of images of text when possible');
    recommendations.push(
      'Reserve images of text for logos, branding, or essential visual presentation',
    );
    recommendations.push('Provide text alternatives that include the same text as the image');
    recommendations.push('Use CSS for visual text styling instead of images');
    recommendations.push('Ensure images of text can be resized without loss of quality');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(image: any): string {
    if (image.isEssential) {
      return `<img src="${image.src}" alt="${image.alt}">`;
    }
    return `<img src="text-image.png" alt="Welcome to Our Website">`;
  }

  private getAfterExample(image: any): string {
    if (image.isEssential) {
      return `<img src="${image.src}" alt="${image.alt}" role="img">`;
    }
    return '<h1>Welcome to Our Website</h1>';
  }

  private getFixDescription(image: { src?: string; alt?: string; tagName?: string; [key: string]: unknown }): string {
    if (image.isEssential) {
      return 'Essential images of text are acceptable but should have proper alt text';
    }
    if (!image.hasTextualAlternative) {
      return 'Replace image of text with actual HTML text';
    }
    return 'Consider using CSS styling instead of images for text presentation';
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'non_essential':
        return `
<!-- Before: Image of text -->
<img src="welcome-heading.png" alt="Welcome to Our Website" width="400" height="60">

<!-- After: Actual HTML text with CSS styling -->
<h1 class="welcome-heading">Welcome to Our Website</h1>
<style>
.welcome-heading {
  font-family: 'Arial', sans-serif;
  font-size: 2.5rem;
  color: #2c3e50;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  margin: 20px 0;
}
</style>
        `;
      case 'essential':
        return `
<!-- Before: Logo without proper identification -->
<img src="company-logo.png" alt="Logo">

<!-- After: Logo with proper alt text -->
<img src="company-logo.png" alt="Acme Corporation" role="img">

<!-- Alternative: SVG logo with text -->
<svg role="img" aria-labelledby="logo-title">
  <title id="logo-title">Acme Corporation</title>
  <!-- SVG content -->
</svg>
        `;
      case 'background_image':
        return `
<!-- Before: Background image with text -->
<div class="hero" style="background-image: url('hero-text.jpg'); height: 300px;">
</div>

<!-- After: Actual text with background styling -->
<div class="hero">
  <h1>Transform Your Business</h1>
  <p>Innovative solutions for modern challenges</p>
</div>
<style>
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
}
</style>
        `;
      default:
        return 'Use actual HTML text with CSS styling instead of images of text';
    }
  }
}
